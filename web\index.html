<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Mood Box - Movie Discovery App. Discover movies, TV shows, and connect with friends.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Mood Box">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <title>Mood Box - Movie Discovery App</title>
  <link rel="manifest" href="manifest.json">

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-firestore.js"></script>

  <!-- Loading styles -->
  <style>
    .loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      color: white;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .loading-logo {
      font-size: 3rem;
      margin-bottom: 2rem;
      animation: pulse 2s infinite;
    }

    .loading-text {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.8;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid #ff4444;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>

  <script>
    // Your web app's Firebase configuration
    const firebaseConfig = {
      // Add your Firebase config here from the Firebase Console
      apiKey: "your-api-key",
      authDomain: "your-auth-domain",
      projectId: "your-project-id",
      storageBucket: "your-storage-bucket",
      messagingSenderId: "your-messaging-sender-id",
      appId: "your-app-id"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
  </script>
</head>

<body>
  <script src="flutter_bootstrap.js" async></script>
</body>

</html>